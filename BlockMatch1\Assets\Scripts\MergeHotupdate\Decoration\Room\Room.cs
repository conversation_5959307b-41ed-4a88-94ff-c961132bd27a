using System;
using System.Collections;
using System.Collections.Generic;
using DragonU3DSDK;
using UnityEngine;
using RoomItemId = System.String;
using RoomId = System.Int32;
using RoomResId = System.Int32;
using RoomNodeId = System.Int64;
using DG.Tweening;
using DragonPlus.Core;
using Framework;
using TMGame;
using TMGame.Storage;
namespace DecorationRom.Core
{
    public partial class Room
    {
        private RoomGraphic _graphic;
        private RoomData _data;
        private Camera _camera;
        private RoomId _roomId;
        private RoomResId _roomResId;
        private Dictionary<RoomNodeId, RoomNode> _nodeDic = new Dictionary<RoomNodeId, RoomNode>();
        private float _cameraOrthographicSize;

        //因为聚焦，尺寸，中心点都在变化
        private static Vector2 _dynamicCenterPosition;
        private static float _dynamicDesignWidth;
        private static float _dynamicDesignHeight;

        public Transform transform => _graphic.transform;

        public RoomId Id
        {
            get => _roomId;
        }

        public RoomResId ResId
        {
            get => _roomResId;
        }

        public Camera Camera
        {
            get => _camera;
            set => _camera = value;
        }

        public int NodeCount
        {
            get => _nodeDic.Count;
        }

        public static float DesignWidth
        {
            get => _dynamicDesignWidth;
        }

        public static float DesignHeight
        {
            get => _dynamicDesignHeight;
        }

        public static Vector2 CenterPosition
        {
            get => _dynamicCenterPosition;
        }

        public Dictionary<long, RoomNode> AllNodesDic
        {
            get => _nodeDic;
            set => _nodeDic = value;
        }

        public RoomData Data
        {
            get => _data;
        }

        private bool _initedGraphic = false;
        private CoroutineHandler _initingGraphicCoroutine;

        public Room(RoomId roomId, RoomResId resId)
        {
            _roomId = roomId;
            _roomResId = resId;
            _camera = Camera.main;
            _data = new RoomData(roomId, resId);

            initCamera();
        }

        public void InitGraphic(bool isInit = true)
        {
            InitNodeStorage();
            UpdateNodeStorage();
            _initingGraphicCoroutine = GameGlobal.GetMod<ModCoroutine>().StartCoroutine(_InitGraphic(isInit));
        }

        public IEnumerator _InitGraphic(bool isInit)
        {
            _initedGraphic = false;
            _graphic = new RoomGraphic(this);
            yield return GameGlobal.GetMod<ModCoroutine>().StartCoroutine(initNodes());
            if (isInit)
                yield return GameGlobal.GetMod<ModCoroutine>().StartCoroutine(initItemFromStorage());
            else
                ShowAllOldItem();

            _initedGraphic = true;
        }

        private void initCamera()
        {
            resetCamera();
            _camera.transform.Reset();
            _camera.transform.transform.localPosition = new Vector3(0, 0, Room.CAMERA_BASE_Z);

            _camera.orthographic = true;
            _cameraOrthographicSize = CalculateOrthographicSize(Room.DESIGN_WIDTH, Room.DESIGN_HEIGHT);
            _camera.orthographicSize = _cameraOrthographicSize;
        }

        private void resetCamera()
        {
            _dynamicCenterPosition = Vector2.zero;
            _dynamicDesignWidth = Room.DESIGN_WIDTH;
            _dynamicDesignHeight = Room.DESIGN_HEIGHT;
        }

        private float CalculateOrthographicSize(float width, float height)
        {
            var currentX = width / 100f / 2f;
            var currentY = height / 100f / 2f;

            var size = 0f;

            var aspectScreen = Screen.width / (float)Screen.height;
            var aspectDesign = width / height;


            if (aspectScreen > aspectDesign)
            {
                size = currentX / _camera.aspect;
            }
            else
            {
                size = currentY;
            }

            if ((float)Screen.height / Screen.width > 1.5f)
            {
                // 同步 UICamera Size 防止引导手位置不对
                // if(TileDebugTools.Instance!=null && TileDebugTools.Instance.uiCamera!=null)
                //     TileDebugTools.Instance.uiCamera.orthographicSize = size;
                if (Camera.main != null) Camera.main.orthographicSize = size;
            }
            return size;
        }

        Dictionary<int, TableRoomNode> _tmpCacheDict = new Dictionary<RoomId, TableRoomNode>();
        /// <summary>
        /// 初始化房间挂点位置
        /// </summary>
        private IEnumerator initNodes()
        {
            var nodeOffsetConfig = TableConfigManage.Instance.GetRoomNodeViewConfig(ResId);
            if (nodeOffsetConfig == null)
            {
                CLog.Error($"_roomId:{_roomId} nodeOffsetConfig is null ");
                yield break;
            }

            List<TableRoomNode> nodeGameConfigList = TableConfigManage.Instance.GetTableRoomNode(_roomId);
            if (nodeGameConfigList == null)
            {
                CLog.Error($"_roomId:{_roomId} nodeGameConfigList is null ");
                yield break;
            }

            _tmpCacheDict.Clear();
            nodeGameConfigList.ForEach(itemData =>
            {
                if (_tmpCacheDict.ContainsKey(itemData.id))
                    return;
                _tmpCacheDict[itemData.id] = itemData;

                int resRoomNodeId = itemData.id % 100 + ResId * 100;
                if (_tmpCacheDict.ContainsKey(resRoomNodeId))
                    return;
                _tmpCacheDict[resRoomNodeId] = itemData;
            });

            if (nodeOffsetConfig.roomNodes == null)
            {
                CLog.Error($"_roomId:{_roomId} nodeOffsetConfig.roomNodes is null ");
                yield break;
            }

            foreach (var viewConfig in nodeOffsetConfig.roomNodes)
            {
                if (_tmpCacheDict.ContainsKey((int)viewConfig.id) == false)
                {
                    CLog.Error("gameconfig not found :" + viewConfig.id);
                    continue;
                }
                var gameConfig = _tmpCacheDict[(int)viewConfig.id];
                if (gameConfig == null)
                {
                    CLog.Error("gameconfig is null:" + viewConfig.id);
                    continue;
                }

                var node = new RoomNode(viewConfig, gameConfig, this);
                _nodeDic.Add(viewConfig.id, node);
                if (_nodeDic.ContainsKey(gameConfig.id))
                    continue;
                _nodeDic[gameConfig.id] = node;
            }
        }

        private void releaseNodes()
        {
            _nodeDic?.Clear();
        }

        public void GetItem(RoomNodeId nodeId, bool change, bool playEffect)
        {
            _data.StorageRoomNode(nodeId);

            RoomNode node = GetNode(nodeId);

            StorageNode storageNode = _data.GetNodeStorage(nodeId);
            if (storageNode.SelectId.IsEmptyString())
            {
                return;
            }

            RoomItem roomItem = node.GetItem(storageNode.SelectId);

            if (roomItem == null)
            {
                CLog.Info("roomItem not found:" + nodeId);
                return;
            }
            SetItem(nodeId, roomItem.Id, change, playEffect);
        }

        public void SetNodeActive(RoomNodeId id, bool active)
        {
            RoomNode node = GetNode(id);
            if (node == null)
                return;

            if (node.transform == null)
                return; ;

            node.transform.gameObject.SetActive(active);
        }

        public void PlayAnim(int nodeId)
        {
            RoomNode node = GetNode(nodeId);

            RoomItem roomItem = node.GetItem(0);
            if (roomItem.IsOld)
                roomItem = node.GetItem(1);

            if (roomItem == null)
            {
                CLog.Info("roomItem not found:" + nodeId);
                return;
            }

            roomItem.PlayAnim();
        }
        public void SetItem(RoomNodeId nodeId, RoomItemId itemId, bool change, bool playEffect)
        {
            if (!_nodeDic.ContainsKey(nodeId))
                return;

            var node = _nodeDic[nodeId];
            node.SetItem(itemId, change, playEffect);
        }

        public RoomNode GetNode(RoomNodeId nodeID)
        {
            if (_nodeDic.ContainsKey(nodeID))
            {
                return _nodeDic[nodeID];
            }

            CLog.Info("Node not found:" + nodeID);
            return null;
        }

        public void BuyNode(RoomNodeId nodeID)
        {
            StorageNode storageNode = _data.GetNodeStorage(nodeID);
            if (storageNode == null)
                return;

            storageNode.Status = (int)RoomItem.Status.UnLock;
        }

        public void UpdateNodeSelect(RoomNodeId nodeID, string selectId)
        {
            StorageNode storageNode = _data.GetNodeStorage(nodeID);
            if (storageNode == null)
                return;

            storageNode.Status = (int)RoomItem.Status.Received;
            storageNode.SelectId = selectId;
        }
        public void FocusOff()
        {
            var duration = 0.5f;
            Camera.transform.DOMove(new Vector3(0, 0, CAMERA_BASE_Z), duration);
            Camera.DOOrthoSize(_cameraOrthographicSize, duration);
            resetCamera();
        }

        public void FocusOn(RoomNodeId nodeId)
        {
            FocusOn(GetNode(nodeId));
        }
        public void FocusOn(RoomNode node)
        {
            var duration = 0.5f;
            if (node.Config.focusScale > 0.1f)
            {
                float focusX = node.Config.focusX;
                float focusY = node.Config.focusY;

                var cameraEndPos = new Vector3(focusX, focusY, CAMERA_BASE_Z);

                float maxOffset = Mathf.Max(Mathf.Abs(focusX), Mathf.Abs(focusY));
                float adapterScale = maxOffset / 0.1f * 0.2f;
                float initScale = CalculateOrthographicSize(Room.DESIGN_WIDTH, Room.DESIGN_HEIGHT);
                float limitScale = initScale - adapterScale;

                if (node.Config.focusScale <= limitScale)
                {
                    Camera.DOOrthoSize(node.Config.focusScale, duration);
                    Camera.transform.DOMove(cameraEndPos, duration);
                    return;
                }
            }

            if (node.CurrentItem == null)
                return;

            var cameraPos = new Vector3(0, 0, CAMERA_BASE_Z);
            var targetPos = new Vector3(node.CurrentItem.WorldPosition.x, node.CurrentItem.WorldPosition.y, 0);
            var maxScale = 1f / 3f;

            var distance = Vector3.Distance(cameraPos, targetPos) * maxScale;
            var endPos = Vector3.MoveTowards(cameraPos, targetPos, distance);
            var newCenter = node.Offset * maxScale;
            _dynamicDesignWidth = (Room.DESIGN_WIDTH / 2f - Mathf.Abs(newCenter.x)) * 2;
            _dynamicDesignHeight = (Room.DESIGN_HEIGHT / 2f - Mathf.Abs(newCenter.y)) * 2;
            var targetoorthographicSize = CalculateOrthographicSize(_dynamicDesignWidth, _dynamicDesignHeight);

            Camera.transform.DOMove(endPos, duration);
            Camera.DOOrthoSize(targetoorthographicSize, duration);

            _dynamicCenterPosition = newCenter;
        }

        public Vector3 GetRoomNodeScreenPos(RoomNodeId nodeId)
        {
            var node = GetNode(nodeId);
            return node == null ? Vector3.zero : node.ScreenPos;
        }

        private IEnumerator initItemFromStorage()
        {
            StorageRoom storageRoom = _data.GetStorageRoom();

            foreach (var kv in _nodeDic)
            {
                var node = kv.Value;

                if (storageRoom.RoomNodes.ContainsKey(node.Id) &&
                    storageRoom.RoomNodes[node.Id].Status == (int)RoomItem.Status.Received)
                {
                    GetItem(node.Id, false, false);
                }
                else
                {
                    node.ShowOldItem();
                }
            }

            yield break;
        }

        public void UpdateNodeIfParentIs(RoomNodeId nodeId)
        {
            foreach (var kv in _nodeDic)
            {
                var node = kv.Value;
                if (node.ParentId == nodeId)
                {
                    node.UpdateItemOffset();
                }
            }
        }

        public bool IsCurrentRoomReady()
        {
            if (!_graphic.IsReady()) return false;

            if (!_initedGraphic)
            {
                return false;
            }

            foreach (var kv in _nodeDic)
            {
                if (!kv.Value.IsReady())
                {
                    return false;
                }
            }

            return true;
        }

        public void ClearGraphic()
        {
            try
            {
                if (_initingGraphicCoroutine != null)
                {
                    GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_initingGraphicCoroutine);
                    _initingGraphicCoroutine = null;
                }

                if (_graphic != null)
                    _graphic.Dispose();
                releaseNodes();
            }
            catch (Exception e)
            {
                CLog.Error(e);
            }
        }

        public void ClearAllGraphic()
        {
            foreach (var kv in _nodeDic)
            {
                var node = kv.Value;

                if (node.Config.isClear)
                    continue;

                node.ClearGraphic();
            }
        }


        public void PlayCleanRoomAnim(TableRoomNode roomNodeConfig, Action animEnd = null)
        {
            if (roomNodeConfig == null || !roomNodeConfig.isClear)
            {
                if (animEnd != null)
                    animEnd();

                return;
            }

            RoomNode roomNode = GetNode(roomNodeConfig.id);
            if (roomNode == null)
            {
                if (animEnd != null)
                    animEnd();
                return;
            }

            roomNode.PlayCleanRoomAnim(animEnd);
        }

        public void PlayEffect()
        {
            foreach (var kv in _nodeDic)
            {
                var node = kv.Value;

                node.PlayEffect();
            }
        }

        public void StopEffect()
        {
            foreach (var kv in _nodeDic)
            {
                var node = kv.Value;

                node.StopEffect();
            }
        }

        public void ShowAllOldItem()
        {
            foreach (var kv in _nodeDic)
            {
                var node = kv.Value;
                bool isShowOld = true;

                if (node.Config.isClear)
                    continue;

                if (!node.Config.oneTimeClean)
                    node.ShowOldItem();

                node.SetShowNodes(false);
            }
        }

        public void Show()
        {
            if (_graphic == null)
                return;

            _graphic.Show();
        }

        public void Hide()
        {
            if (_graphic == null)
                return;

            _graphic.Hide();
        }

        private void InitNodeStorage()
        {
            if(_roomId == 9000) return;
            List<TableRoomNode> roomNodes = TableConfigManage.Instance.GetTableRoomNode(_roomId);

            StorageRoom storageRoom = _data.GetStorageRoom();

            foreach (var kv in roomNodes)
            {
                if (storageRoom.RoomNodes.ContainsKey(kv.id))
                    continue;

                StorageNode storageNode = new StorageNode();
                storageNode.Id = kv.id;
                storageNode.Status = kv.isOpen ? (int)RoomItem.Status.UnLock : (int)RoomItem.Status.Lock;

                storageRoom.RoomNodes.Add(storageNode.Id, storageNode);
            }
        }

        private void UpdateNodeStorage()
        {
            if(_roomId == 9000) return;
            List<TableRoomNode> roomNodes = TableConfigManage.Instance.GetTableRoomNode(_roomId);

            StorageRoom storageRoom = _data.GetStorageRoom();

            foreach (var kv in roomNodes)
            {
                if (!storageRoom.RoomNodes.ContainsKey(kv.id))
                    continue;

                if (RoomManager.Instance.RoomChapter == 1001)
                {
                    StorageNode storageNode = storageRoom.RoomNodes[kv.id];
                    if (storageNode.Status != (int)RoomItem.Status.Received)
                        continue;

                    if (kv.nextNode == null)
                        continue;
                    foreach (var nodeId in kv.nextNode)
                    {
                        StorageNode nextNode = _data.GetNodeStorage(nodeId);
                        if (nextNode == null)
                            continue;

                        if (nextNode.Status == (int)RoomItem.Status.Lock)
                        {
                            nextNode.Status = (int)RoomItem.Status.UnLock;
                            Debug.LogWarning("挂点状态重置 " + _roomId + "\t" + nodeId);
                        }
                    }
                }
                else
                {
                    if (kv.preNode == null)
                        continue;

                    int receiveCount = 0;
                    foreach (var nodeId in kv.preNode)
                    {
                        StorageNode nextNode = _data.GetNodeStorage(nodeId);
                        if (nextNode == null)
                            continue;

                        if (nextNode.Status == (int)RoomItem.Status.Received)
                            receiveCount++;
                        else if(nodeId <= 0)//特殊节点只计数
                            receiveCount++;
                    }

                    if (receiveCount != kv.preNode.Length)
                        continue;

                    StorageNode storageNode = storageRoom.RoomNodes[kv.id];
                    if (storageNode.Status == (int)RoomItem.Status.Lock)
                    {
                        storageNode.Status = (int)RoomItem.Status.UnLock;
                        Debug.LogWarning("挂点状态重置 " + _roomId + "\t" + kv.id);
                    }
                }
            }
        }

        public RoomNode TapNode(Vector3 screenPos)
        {
            if (!Camera.orthographic)
            {
                screenPos.z = 0f - Camera.transform.position.z;
            }

            RoomNode selectedNode = null;
            foreach (var node in _nodeDic)
            {
                if (node.Value.TouchMe(screenPos))
                {
                    if (selectedNode == null)
                    {
                        selectedNode = node.Value;
                    }
                    else if (node.Value.RenderQueue > selectedNode.RenderQueue)
                    {
                        selectedNode = node.Value;
                    }
                }
            }

            if (selectedNode != null)
            {
                selectedNode.CurrentItem.OnTap();
            }

            return selectedNode;
        }

    }
}