using System.Collections.Generic;
using System.Linq;
using DecorationRom.Core;
using Framework;
using TMGame;
using TMGame.Storage;
using UnityEngine;
using UnityEngine.UI;

public class UIView_ChooseRoom : UIView_ChooseRoomBase
{
    private ScrollRect scrollRect;
    private List<ChooseRoomItem> roomItems = new List<ChooseRoomItem>();

    protected override void OnCreate()
    {
        base.OnCreate();
        // 禁用多指操作
        Input.multiTouchEnabled = false;
        Debug.Log("禁用多指操作");
        TMUtility.NotchAdapte(this.UINode_Main);
        UIBtn_BackButton.onClick.AddListener(OnClickClose);
        scrollRect = UINode_RoomGroup.transform.Find("Scroll View").GetComponent<ScrollRect>();
        UITxt_TextTitle.SetText(CoreUtils.GetLocalization("UI_build_title"));
    }

    private void OnClickClose()
    {
        GameGlobal.GetMgr<SoundMgr>().PlayButtonClick();
        Close();
    }

    protected override void OnOpen()
    {
        base.OnOpen();
        InitRoomList();
    }

    private void InitRoomList()
    {
        var chapter = RoomManager.Instance.CurRoomChapter;
        if (chapter == null || chapter.roomIds == null || chapter.roomIds.Length == 0)
        {
            return;
        }

        int[] roomIds = chapter.roomIds;
        // 解锁检查
        {
            for (int i = 0; i < roomIds.Length; i++)
            {
                if (RoomManager.Instance.StorageHome.RoomData.TryGetValue(roomIds[i],
                        out StorageRoom data))
                {
                    if (i == roomIds.Length - 1)
                        break;
                    if (!data.IsFinish && data.LastProgress >= 1f)
                    {
                        int nextRoomId = roomIds[i + 1];
                        // 检查下一个房间是否为最后一个房间且标记为即将推出
                        var nextRoomConfig = TableConfigManage.Instance.GetTableRoom(nextRoomId);
                        if (nextRoomConfig != null && nextRoomConfig.isComingSoon)
                        {
                            // 如果下一个房间是即将推出的房间，跳过解锁
                            continue;
                        }
                        RoomManager.Instance.UnLockRoom(nextRoomId);
                    }
                }
            }
        }

        // Hide all existing items
        foreach (var item in roomItems)
        {
            item.Visible = false;
        }

        int roomId = RoomManager.Instance.GetLastUnLockRoomId();

        int normalIndex = -1;
        for (int i = roomIds.Length - 1; i >= 0; i--)  // Reverse loop
        {
            ChooseRoomItem item;
            int currentIndex = roomIds.Length - 1 - i;
        
            if (currentIndex < roomItems.Count)
            {
                item = roomItems[currentIndex];
                item.Visible = true;
            }
            else
            {
                item = OpenUIWidget<ChooseRoomItem>(UINode_AreaContent.transform, false);
                roomItems.Add(item);
            }

            item.SetData(roomIds[i]);

            if (roomId == roomIds[i])
            {
                normalIndex = currentIndex;
            }
        }

        ScrollToIndex(normalIndex);
    }

    public void ScrollToIndex(int index)
    {
        if (index < 0 || index >= roomItems.Count)
        {
            Debug.LogWarning("Invalid index");
            return;
        }

        Vector2 anchorPos = UINode_AreaContent.anchoredPosition;
        anchorPos.y = index * 500; // Adjusted to scroll upwards from the bottom
        UINode_AreaContent.anchoredPosition = anchorPos;
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        Input.multiTouchEnabled = true;
        Debug.Log("启用多指操作");
    }
}
